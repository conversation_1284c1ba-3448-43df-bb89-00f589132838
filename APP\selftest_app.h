/* Licence
* Company: MCUSTUDIO
* Auther: Ah<PERSON><PERSON><PERSON>.
* Version: V0.10
* Time: 2025/06/14
* Note: 系统自检应用层头文件
*/

#ifndef SELFTEST_APP_H
#define SELFTEST_APP_H

#include "mcu_cmic_gd32f470vet6.h"

// 自检结果枚举
typedef enum {
    SELFTEST_OK = 0,
    SELFTEST_FAIL = 1
} selftest_result_t;

// Flash芯片型号识别结构
typedef struct {
    uint32_t id;
    const char* name;
} flash_chip_info_t;

// 函数声明
void system_selftest(void); // 系统自检主函数
selftest_result_t flash_selftest(void); // Flash自检
selftest_result_t tf_card_selftest(void); // TF卡自检
const char* get_flash_chip_name(uint32_t flash_id); // 获取Flash芯片型号名称

#endif // SELFTEST_APP_H
