File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.011124%,13439,96,12888,551,0,96
ff.o,9.650874%,7201,6,7182,13,6,0
sdio_sdcard.o,9.609328%,7170,68,7134,0,36,32
oled.o,5.328687%,3976,22,1242,2712,22,0
usart_app.o,4.259197%,3178,258,3176,0,2,256
sampling_app.o,3.457750%,2580,146,2564,0,16,130
config_app.o,2.960531%,2209,13,2196,0,13,0
btod.o,2.884139%,2152,0,2152,0,0,0
ebtn.o,2.774241%,2070,60,2070,0,0,60
fz_wm.l,2.039804%,1522,0,1506,16,0,0
mcu_cmic_gd32f470vet6.o,1.929907%,1440,288,1432,0,8,280
gd32f4xx_dma.o,1.785164%,1332,0,1332,0,0,0
scanf_fp.o,1.704751%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.412585%,1054,0,1054,0,0,0
perf_counter.o,1.326811%,990,80,906,4,80,0
gd25qxx.o,1.326811%,990,0,990,0,0,0
_scanf.o,1.184748%,884,0,884,0,0,0
m_wm.l,1.074851%,802,0,802,0,0,0
_printf_fp_hex.o,1.074851%,802,0,764,38,0,0
scanf_hexfp.o,1.072170%,800,0,800,0,0,0
selftest_app.o,1.056088%,788,0,572,216,0,0
fatfs_unicode.o,0.956912%,714,0,498,216,0,0
system_gd32f4xx.o,0.935469%,698,4,694,0,4,0
btn_app.o,0.884541%,660,196,450,14,196,0
gd32f4xx_adc.o,0.876499%,654,0,654,0,0,0
gd32f4xx_usart.o,0.863097%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.841654%,628,0,628,0,0,0
gd32f4xx_timer.o,0.788045%,588,0,588,0,0,0
rtc_app.o,0.696911%,520,16,504,0,16,0
gd32f4xx_i2c.o,0.664746%,496,0,496,0,0,0
startup_gd32f450_470.o,0.659385%,492,2048,64,428,0,2048
diskio.o,0.552168%,412,0,412,0,0,0
__printf_flags_ss_wp.o,0.548147%,409,0,392,17,0,0
gd32f4xx_rcu.o,0.536085%,400,0,400,0,0,0
bigflt0.o,0.503920%,376,0,228,148,0,0
dmul.o,0.455672%,340,0,340,0,0,0
_scanf_int.o,0.444951%,332,0,332,0,0,0
lc_ctype_c.o,0.423507%,316,0,44,272,0,0
scanf_infnan.o,0.412786%,308,0,308,0,0,0
oled_app.o,0.364538%,272,0,272,0,0,0
narrow.o,0.356497%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.351136%,262,0,262,0,0,0
led_app.o,0.336393%,251,7,244,0,7,0
lludivv7m.o,0.318971%,238,0,238,0,0,0
ldexp.o,0.305569%,228,0,228,0,0,0
gd32f4xx_misc.o,0.289486%,216,0,216,0,0,0
main.o,0.278764%,208,0,208,0,0,0
gd32f4xx_dac.o,0.265362%,198,0,198,0,0,0
_printf_wctomb.o,0.262682%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.251960%,188,0,148,40,0,0
scheduler.o,0.251960%,188,88,100,0,88,0
gd32f4xx_it.o,0.241238%,180,0,180,0,0,0
_printf_intcommon.o,0.238558%,178,0,178,0,0,0
systick.o,0.225156%,168,4,164,0,4,0
strtod.o,0.219795%,164,0,164,0,0,0
dnaninf.o,0.209073%,156,0,156,0,0,0
perfc_port_default.o,0.206393%,154,0,154,0,0,0
strncmp.o,0.201032%,150,0,150,0,0,0
frexp.o,0.187630%,140,0,140,0,0,0
fnaninf.o,0.187630%,140,0,140,0,0,0
rt_memcpy_v6.o,0.184949%,138,0,138,0,0,0
lludiv10.o,0.184949%,138,0,138,0,0,0
init_alloc.o,0.184949%,138,0,138,0,0,0
strcmpv7m.o,0.171547%,128,0,128,0,0,0
_printf_fp_infnan.o,0.171547%,128,0,128,0,0,0
_printf_longlong_dec.o,0.166186%,124,0,124,0,0,0
dleqf.o,0.160826%,120,0,120,0,0,0
deqf.o,0.160826%,120,0,120,0,0,0
_printf_dec.o,0.160826%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.150104%,112,0,112,0,0,0
drleqf.o,0.144743%,108,0,108,0,0,0
gd32f4xx_spi.o,0.139382%,104,0,104,0,0,0
retnan.o,0.134021%,100,0,100,0,0,0
rt_memcpy_w.o,0.134021%,100,0,100,0,0,0
d2f.o,0.131341%,98,0,98,0,0,0
h1_alloc.o,0.125980%,94,0,94,0,0,0
scalbn.o,0.123300%,92,0,92,0,0,0
__dczerorl2.o,0.120619%,90,0,90,0,0,0
memcmp.o,0.117939%,88,0,88,0,0,0
f2d.o,0.115258%,86,0,86,0,0,0
strncpy.o,0.115258%,86,0,86,0,0,0
_printf_str.o,0.109897%,82,0,82,0,0,0
defsig_rtmem_inner.o,0.107217%,80,0,80,0,0,0
rt_memclr_w.o,0.104537%,78,0,78,0,0,0
h1_free.o,0.104537%,78,0,78,0,0,0
_printf_pad.o,0.104537%,78,0,78,0,0,0
sys_stackheap_outer.o,0.099176%,74,0,74,0,0,0
llsdiv.o,0.096495%,72,0,72,0,0,0
lc_numeric_c.o,0.096495%,72,0,44,28,0,0
rt_memclr.o,0.091134%,68,0,68,0,0,0
dunder.o,0.085774%,64,0,64,0,0,0
_wcrtomb.o,0.085774%,64,0,64,0,0,0
_sgetc.o,0.085774%,64,0,64,0,0,0
strlen.o,0.083093%,62,0,62,0,0,0
__0sscanf.o,0.080413%,60,0,60,0,0,0
atof.o,0.075052%,56,0,56,0,0,0
__2snprintf.o,0.075052%,56,0,56,0,0,0
vsnprintf.o,0.069691%,52,0,52,0,0,0
h1_extend.o,0.069691%,52,0,52,0,0,0
__scatter.o,0.069691%,52,0,52,0,0,0
defsig_general.o,0.067011%,50,0,50,0,0,0
fpclassify.o,0.064330%,48,0,48,0,0,0
trapv.o,0.064330%,48,0,48,0,0,0
_printf_char_common.o,0.064330%,48,0,48,0,0,0
libinit2.o,0.061650%,46,0,46,0,0,0
scanf_char.o,0.058969%,44,0,44,0,0,0
_printf_wchar.o,0.058969%,44,0,44,0,0,0
_printf_char.o,0.058969%,44,0,44,0,0,0
__2sprintf.o,0.058969%,44,0,44,0,0,0
_printf_charcount.o,0.053609%,40,0,40,0,0,0
llshl.o,0.050928%,38,0,38,0,0,0
strstr.o,0.048248%,36,0,36,0,0,0
init_aeabi.o,0.048248%,36,0,36,0,0,0
_printf_truncate.o,0.048248%,36,0,36,0,0,0
systick_wrapper_ual.o,0.042887%,32,0,32,0,0,0
_chval.o,0.037526%,28,0,28,0,0,0
__scatter_zi.o,0.037526%,28,0,28,0,0,0
dcmpi.o,0.032165%,24,0,24,0,0,0
_rserrno.o,0.029485%,22,0,22,0,0,0
strchr.o,0.026804%,20,0,20,0,0,0
adc_app.o,0.026804%,20,0,20,0,0,0
isspace.o,0.024124%,18,0,18,0,0,0
exit.o,0.024124%,18,0,18,0,0,0
fpconst.o,0.021443%,16,0,0,16,0,0
dcheck1.o,0.021443%,16,0,16,0,0,0
rt_ctype_table.o,0.021443%,16,0,16,0,0,0
_snputc.o,0.021443%,16,0,16,0,0,0
sys_wrch.o,0.018763%,14,0,14,0,0,0
h1_init.o,0.018763%,14,0,14,0,0,0
defsig_rtmem_outer.o,0.018763%,14,0,14,0,0,0
__printf_wp.o,0.018763%,14,0,14,0,0,0
sd_app.o,0.018763%,14,0,14,0,0,0
dretinf.o,0.016083%,12,0,12,0,0,0
sys_exit.o,0.016083%,12,0,12,0,0,0
__rtentry2.o,0.016083%,12,0,12,0,0,0
fretinf.o,0.013402%,10,0,10,0,0,0
fpinit.o,0.013402%,10,0,10,0,0,0
rtexit2.o,0.013402%,10,0,10,0,0,0
defsig_exit.o,0.013402%,10,0,10,0,0,0
_sputc.o,0.013402%,10,0,10,0,0,0
_printf_ll.o,0.013402%,10,0,10,0,0,0
_printf_l.o,0.013402%,10,0,10,0,0,0
scanf2.o,0.010722%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010722%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010722%,8,0,8,0,0,0
libspace.o,0.010722%,8,96,8,0,0,96
__main.o,0.010722%,8,0,8,0,0,0
istatus.o,0.008041%,6,0,6,0,0,0
heapauxi.o,0.008041%,6,0,6,0,0,0
_printf_x.o,0.008041%,6,0,6,0,0,0
_printf_u.o,0.008041%,6,0,6,0,0,0
_printf_s.o,0.008041%,6,0,6,0,0,0
_printf_p.o,0.008041%,6,0,6,0,0,0
_printf_o.o,0.008041%,6,0,6,0,0,0
_printf_n.o,0.008041%,6,0,6,0,0,0
_printf_ls.o,0.008041%,6,0,6,0,0,0
_printf_llx.o,0.008041%,6,0,6,0,0,0
_printf_llu.o,0.008041%,6,0,6,0,0,0
_printf_llo.o,0.008041%,6,0,6,0,0,0
_printf_lli.o,0.008041%,6,0,6,0,0,0
_printf_lld.o,0.008041%,6,0,6,0,0,0
_printf_lc.o,0.008041%,6,0,6,0,0,0
_printf_i.o,0.008041%,6,0,6,0,0,0
_printf_g.o,0.008041%,6,0,6,0,0,0
_printf_f.o,0.008041%,6,0,6,0,0,0
_printf_e.o,0.008041%,6,0,6,0,0,0
_printf_d.o,0.008041%,6,0,6,0,0,0
_printf_c.o,0.008041%,6,0,6,0,0,0
_printf_a.o,0.008041%,6,0,6,0,0,0
__rtentry4.o,0.008041%,6,0,6,0,0,0
scanf1.o,0.005361%,4,0,4,0,0,0
printf2.o,0.005361%,4,0,4,0,0,0
printf1.o,0.005361%,4,0,4,0,0,0
hguard.o,0.005361%,4,0,4,0,0,0
_printf_percent_end.o,0.005361%,4,0,4,0,0,0
use_no_semi.o,0.002680%,2,0,2,0,0,0
rtexit.o,0.002680%,2,0,2,0,0,0
libshutdown2.o,0.002680%,2,0,2,0,0,0
libshutdown.o,0.002680%,2,0,2,0,0,0
libinit.o,0.002680%,2,0,2,0,0,0
