/* Licence
* Company: MCUSTUDIO
* Auther: Ahy<PERSON><PERSON>.
* Version: V0.10
* Time: 2025/06/05
* Note: RTC应用层实现 - 显示时钟并支持串口发送
*/

#include "mcu_cmic_gd32f470vet6.h"
#include "rtc_app.h"

// 全局变量定义
rtc_time_t current_time = {0};
uint8_t rtc_uart_send_flag = 0;  // 0: 停止发送, 1: 开始发送

// 私有变量
static uint32_t last_second_update = 0;
static uint32_t last_uart_send = 0;

/**
 * @brief RTC应用初始化
 * 设置初始时间，可以通过修改这里的参数来设置时钟
 */
void rtc_app_init(void)
{
    // 设置初始时间 - 可以通过修改这些参数来设置时钟
    // 格式: 年(20xx), 月(1-12), 日(1-31), 时(0-23), 分(0-59), 秒(0-59)
    rtc_set_time(25, 1, 15, 14, 30, 0);  // 2025年1月15日 14:30:00

    rtc_uart_send_flag = 0;  // 初始状态不发送
    last_second_update = get_system_ms();
    last_uart_send = get_system_ms();
}

/**
 * @brief 设置时钟时间
 * @param year 年份 (20xx年的后两位，如25表示2025年)
 * @param month 月份 (1-12)
 * @param day 日期 (1-31)
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0-59)
 * @param second 秒钟 (0-59)
 */
void rtc_set_time(uint8_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second)
{
    current_time.year = year;
    current_time.month = month;
    current_time.day = day;
    current_time.hour = hour;
    current_time.minute = minute;
    current_time.second = second;

    // 重置时间更新计数器，确保时间从设置的时刻开始准确计时
    last_second_update = get_system_ms();
}

/**
 * @brief 获取格式化的时间字符串
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 */
void rtc_get_time_string(char* buffer, uint8_t buffer_size)
{
    snprintf(buffer, buffer_size, "20%02d-%02d-%02d %02d:%02d:%02d",
             current_time.year, current_time.month, current_time.day,
             current_time.hour, current_time.minute, current_time.second);
}

/**
 * @brief 开始串口发送时钟
 */

/**
 * @brief 停止串口发送时钟
 */


/**
 * @brief 更新时钟 - 每秒调用一次
 */
static void rtc_update_time(void)
{
    current_time.second++;

    if (current_time.second >= 60) {
        current_time.second = 0;
        current_time.minute++;

        if (current_time.minute >= 60) {
            current_time.minute = 0;
            current_time.hour++;

            if (current_time.hour >= 24) {
                current_time.hour = 0;
                current_time.day++;

                // 简单的月份天数处理
                uint8_t days_in_month = 31;
                if (current_time.month == 2) {
                    // 简化处理，不考虑闰年
                    days_in_month = 28;
                } else if (current_time.month == 4 || current_time.month == 6 ||
                          current_time.month == 9 || current_time.month == 11) {
                    days_in_month = 30;
                }

                if (current_time.day > days_in_month) {
                    current_time.day = 1;
                    current_time.month++;

                    if (current_time.month > 12) {
                        current_time.month = 1;
                        current_time.year++;
                        if (current_time.year > 99) {
                            current_time.year = 0;
                        }
                    }
                }
            }
        }
    }
}

/**
 * @brief RTC任务 - 由调度器定期调用
 */
void rtc_task(void)
{
    uint32_t current_ms = get_system_ms();

    // 每1000ms更新一次时钟
    if (current_ms - last_second_update >= 1000) {
        rtc_update_time();
        last_second_update = current_ms;
    }

    // 如果启用了串口发送，每1000ms发送一次时钟信息
    if (rtc_uart_send_flag && (current_ms - last_uart_send >= 1000)) {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        my_printf(DEBUG_USART, "Current Time: %s\r\n", time_str);
        last_uart_send = current_ms;
    }
}
