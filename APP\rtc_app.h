#ifndef __RTC_APP_H__
#define __RTC_APP_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 时钟结构体
typedef struct {
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
    uint8_t year;
    uint8_t month;
    uint8_t day;
} rtc_time_t;

// 全局变量声明
extern rtc_time_t current_time;
extern uint8_t rtc_uart_send_flag;

// 函数声明
void rtc_app_init(void);
void rtc_task(void);
void rtc_set_time(uint8_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second);
void rtc_get_time_string(char* buffer, uint8_t buffer_size);
void rtc_start_uart_send(void);
void rtc_stop_uart_send(void);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H__ */
