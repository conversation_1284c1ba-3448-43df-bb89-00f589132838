/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 配置文件管理应用层实现
*/

#include "config_app.h"

// 全局变量定义
config_params_t config_params = {50.0f, 100.0f, CONFIG_MAGIC_NUMBER}; // 默认值：ratio=50, limit=100
config_state_t config_state = CONFIG_STATE_NORMAL;

void config_app_init(void)
{
    config_state = CONFIG_STATE_NORMAL;

    // 尝试从Flash读取配置参数
    if(!config_read_from_flash()) {
        // 如果Flash中没有有效数据，使用默认值
        config_params.ratio = 50.0f;
        config_params.limit = 100.0f; // 默认limit改为100
        config_params.magic = CONFIG_MAGIC_NUMBER;
    }
}

int config_read_from_tf(void)
{
    FIL config_file;
    FRESULT result;
    char file_buffer[256];
    UINT bytes_read;
    char* line_start;
    char* line_end;

    // 尝试打开配置文件
    result = f_open(&config_file, "0:/TF_demo/config.ini", FA_READ);
    if(result != FR_OK) {
        return 0; // 文件不存在或打开失败
    }

    // 读取整个文件内容
    result = f_read(&config_file, file_buffer, sizeof(file_buffer) - 1, &bytes_read);
    f_close(&config_file);

    if(result != FR_OK) {
        return 0; // 读取失败
    }

    file_buffer[bytes_read] = '\0'; // 确保字符串结束

    // 逐行解析文件内容
    line_start = file_buffer;
    while(line_start < file_buffer + bytes_read) {
        // 找到行结束位置
        line_end = strchr(line_start, '\n');
        if(line_end == NULL) {
            line_end = file_buffer + bytes_read; // 最后一行
        }

        // 创建当前行的字符串
        int line_len = line_end - line_start;
        if(line_len > 0) {
            char line_buffer[128];
            if(line_len < sizeof(line_buffer)) {
                strncpy(line_buffer, line_start, line_len);
                line_buffer[line_len] = '\0';

                // 移除回车符
                char* carriage = strchr(line_buffer, '\r');
                if(carriage) *carriage = '\0';

                // 解析Ratio参数
                if(strncmp(line_buffer, "Ratio=", 6) == 0) {
                    float ratio_val = atof(line_buffer + 6);
                    if(ratio_val >= 0.0f && ratio_val <= 100.0f) {
                        config_params.ratio = ratio_val;
                    }
                }
                // 解析Limit参数
                else if(strncmp(line_buffer, "Limit=", 6) == 0) {
                    float limit_val = atof(line_buffer + 6);
                    if(limit_val >= 0.0f && limit_val <= 200.0f) {
                        config_params.limit = limit_val;
                    }
                }
            }
        }

        // 移动到下一行
        line_start = line_end + 1;
    }

    return 1; // 成功读取
}

int config_read_from_flash(void)
{
    config_params_t flash_params;

    // 从Flash读取配置参数
    spi_flash_buffer_read((uint8_t*)&flash_params, CONFIG_FLASH_ADDR, sizeof(config_params_t));

    // 验证魔数
    if(flash_params.magic == CONFIG_MAGIC_NUMBER) {
        // 验证参数范围
        if(flash_params.ratio >= 0.0f && flash_params.ratio <= 100.0f &&
           flash_params.limit >= 0.0f && flash_params.limit <= 200.0f) {
            config_params = flash_params;
            return 1; // 成功读取
        }
    }

    return 0; // 读取失败或数据无效
}

int config_save_to_flash(void)
{
    // 设置魔数
    config_params.magic = CONFIG_MAGIC_NUMBER;

    // 擦除Flash扇区
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    // 写入配置参数到Flash
    spi_flash_buffer_write((uint8_t*)&config_params, CONFIG_FLASH_ADDR, sizeof(config_params_t));

    return 1; // 成功保存
}

int config_write_to_tf(void)
{
    FIL config_file;
    FRESULT result;
    UINT bytes_written;
    char write_buffer[128];
    
    // 创建TF_demo目录（如果不存在）
    f_mkdir("0:/TF_demo");
    
    // 打开配置文件进行写入
    result = f_open(&config_file, "0:/TF_demo/config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if(result != FR_OK) {
        return 0; // 打开失败
    }
    
    // 写入Ratio参数
    sprintf(write_buffer, "Ratio=%.2f\r\n", config_params.ratio);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return 0;
    }
    
    // 写入Limit参数
    sprintf(write_buffer, "Limit=%.2f\r\n", config_params.limit);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    if(result != FR_OK) {
        f_close(&config_file);
        return 0;
    }
    
    f_close(&config_file);
    return 1; // 成功写入
}

void config_cmd_handler(void)
{
    if(config_read_from_tf()) {
        my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);
        my_printf(DEBUG_USART, "config read success\r\n");
    } else {
        my_printf(DEBUG_USART, "config ini not found\r\n");
    }
}

void config_save_handler(void)
{
    // 首先读取当前参数（从TF卡或当前内存）
    config_read_from_tf(); // 尝试从TF卡读取最新参数

    // 打印当前参数
    my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
    my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);

    // 保存到Flash
    if(config_save_to_flash()) {
        my_printf(DEBUG_USART, "save parameters to flash\r\n");
    } else {
        my_printf(DEBUG_USART, "save to flash failed\r\n");
    }
}

void config_read_handler(void)
{
    if(config_read_from_flash()) {
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
        my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);
    } else {
        my_printf(DEBUG_USART, "no valid parameters in flash\r\n");
    }
}

void config_ratio_handler(void)
{
    my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
    my_printf(DEBUG_USART, "Input value(0-100)\r\n");
    config_state = CONFIG_STATE_WAIT_RATIO;
}

void config_limit_handler(void)
{
    my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);
    my_printf(DEBUG_USART, "Input value(0~200)\r\n");
    config_state = CONFIG_STATE_WAIT_LIMIT;
}

int config_set_ratio(float new_ratio)
{
    if(new_ratio >= 0.0f && new_ratio <= 100.0f) {
        config_params.ratio = new_ratio;
        config_write_to_tf(); // 保存到文件
        return 1; // 成功
    }
    return 0; // 失败
}

int config_set_limit(float new_limit)
{
    if(new_limit >= 0.0f && new_limit <= 200.0f) {
        config_params.limit = new_limit;
        config_write_to_tf(); // 保存到文件
        return 1; // 成功
    }
    return 0; // 失败
}

void config_process_input(const char* input)
{
    if(config_state == CONFIG_STATE_WAIT_RATIO) {
        float new_ratio = atof(input);
        if(config_set_ratio(new_ratio)) {
            my_printf(DEBUG_USART, "ratio modified success\r\n");
            my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        } else {
            my_printf(DEBUG_USART, "ratio invalid\r\n");
            my_printf(DEBUG_USART, "Ratio = %.2f\r\n", config_params.ratio);
        }
        config_state = CONFIG_STATE_NORMAL;
    }
    else if(config_state == CONFIG_STATE_WAIT_LIMIT) {
        float new_limit = atof(input);
        if(config_set_limit(new_limit)) {
            my_printf(DEBUG_USART, "limit modified success\r\n");
            my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);
        } else {
            my_printf(DEBUG_USART, "limit invalid\r\n");
            my_printf(DEBUG_USART, "Limit = %.2f\r\n", config_params.limit);
        }
        config_state = CONFIG_STATE_NORMAL;
    }
}
