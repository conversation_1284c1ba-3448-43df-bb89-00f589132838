File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.267015%,8849,96,8298,551,0,96
ff.o,12.256088%,6281,8,6260,13,8,0
sdio_sdcard.o,11.863877%,6080,64,6048,0,32,32
oled.o,7.551514%,3870,22,1136,2712,22,0
btod.o,3.777708%,1936,0,1936,0,0,0
ebtn.o,3.321105%,1702,60,1702,0,0,60
gd25qxx.o,3.231346%,1656,0,1656,0,0,0
sd_app.o,2.958164%,1516,1376,1504,0,12,1364
adc_app.o,2.848892%,1460,588,1452,0,8,580
mcu_cmic_gd32f470vet6.o,2.669373%,1368,288,1360,0,8,280
_printf_fp_dec.o,2.056666%,1054,0,1054,0,0,0
_printf_fp_hex.o,1.564939%,802,0,764,38,0,0
perf_counter.o,1.451764%,744,80,660,4,80,0
gd32f4xx_dma.o,1.369810%,702,0,702,0,0,0
gd32f4xx_timer.o,1.143459%,586,0,586,0,0,0
gd32f4xx_usart.o,1.120044%,574,0,574,0,0,0
gd32f4xx_sdio.o,1.065407%,546,0,546,0,0,0
btn_app.o,1.030284%,528,196,318,14,196,0
gd32f4xx_adc.o,1.018576%,522,0,522,0,0,0
system_gd32f4xx.o,1.010771%,518,4,514,0,4,0
usart_app.o,0.979550%,502,258,500,0,2,256
startup_gd32f450_470.o,0.960037%,492,2048,64,428,0,2048
gd32f4xx_i2c.o,0.854667%,438,0,438,0,0,0
__printf_flags_ss_wp.o,0.798080%,409,0,392,17,0,0
fatfs_unicode.o,0.768811%,394,0,286,108,0,0
bigflt0.o,0.733687%,376,0,228,148,0,0
gd32f4xx_rcu.o,0.632220%,324,0,324,0,0,0
diskio.o,0.632220%,324,0,324,0,0,0
lc_ctype_c.o,0.616609%,316,0,44,272,0,0
oled_app.o,0.522947%,268,0,268,0,0,0
gd32f4xx_gpio.o,0.515142%,264,0,264,0,0,0
fz_wm.l,0.499532%,256,0,256,0,0,0
lludivv7m.o,0.464408%,238,0,238,0,0,0
gd32f4xx_dac.o,0.398064%,204,0,204,0,0,0
_printf_wctomb.o,0.382454%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.366844%,188,0,148,40,0,0
_printf_intcommon.o,0.347331%,178,0,178,0,0,0
gd32f4xx_misc.o,0.343428%,176,0,176,0,0,0
systick.o,0.288792%,148,4,144,0,4,0
gd32f4xx_it.o,0.288792%,148,0,148,0,0,0
fnaninf.o,0.273181%,140,0,140,0,0,0
led_app.o,0.271230%,139,7,132,0,7,0
rt_memcpy_v6.o,0.269279%,138,0,138,0,0,0
lludiv10.o,0.269279%,138,0,138,0,0,0
init_alloc.o,0.269279%,138,0,138,0,0,0
scheduler.o,0.265376%,136,64,72,0,64,0
strcmpv7m.o,0.249766%,128,0,128,0,0,0
_printf_fp_infnan.o,0.249766%,128,0,128,0,0,0
_printf_longlong_dec.o,0.241961%,124,0,124,0,0,0
perfc_port_default.o,0.238058%,122,0,122,0,0,0
_printf_dec.o,0.234155%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.218545%,112,0,112,0,0,0
gd32f4xx_spi.o,0.202935%,104,0,104,0,0,0
rt_memcpy_w.o,0.195130%,100,0,100,0,0,0
h1_alloc.o,0.183422%,94,0,94,0,0,0
__dczerorl2.o,0.175617%,90,0,90,0,0,0
memcmp.o,0.171714%,88,0,88,0,0,0
f2d.o,0.167811%,86,0,86,0,0,0
_printf_str.o,0.160006%,82,0,82,0,0,0
main.o,0.160006%,82,0,82,0,0,0
defsig_rtmem_inner.o,0.156104%,80,0,80,0,0,0
rt_memclr_w.o,0.152201%,78,0,78,0,0,0
h1_free.o,0.152201%,78,0,78,0,0,0
_printf_pad.o,0.152201%,78,0,78,0,0,0
sys_stackheap_outer.o,0.144396%,74,0,74,0,0,0
llsdiv.o,0.140493%,72,0,72,0,0,0
lc_numeric_c.o,0.140493%,72,0,44,28,0,0
rt_memclr.o,0.132688%,68,0,68,0,0,0
_wcrtomb.o,0.124883%,64,0,64,0,0,0
strlen.o,0.120980%,62,0,62,0,0,0
vsnprintf.o,0.101467%,52,0,52,0,0,0
h1_extend.o,0.101467%,52,0,52,0,0,0
__scatter.o,0.101467%,52,0,52,0,0,0
defsig_general.o,0.097565%,50,0,50,0,0,0
m_wm.l,0.093662%,48,0,48,0,0,0
fpclassify.o,0.093662%,48,0,48,0,0,0
_printf_char_common.o,0.093662%,48,0,48,0,0,0
libinit2.o,0.089760%,46,0,46,0,0,0
_printf_wchar.o,0.085857%,44,0,44,0,0,0
_printf_char.o,0.085857%,44,0,44,0,0,0
__2sprintf.o,0.085857%,44,0,44,0,0,0
_printf_charcount.o,0.078052%,40,0,40,0,0,0
init_aeabi.o,0.070247%,36,0,36,0,0,0
_printf_truncate.o,0.070247%,36,0,36,0,0,0
systick_wrapper_ual.o,0.062441%,32,0,32,0,0,0
__scatter_zi.o,0.054636%,28,0,28,0,0,0
exit.o,0.035123%,18,0,18,0,0,0
rt_ctype_table.o,0.031221%,16,0,16,0,0,0
aeabi_memset.o,0.031221%,16,0,16,0,0,0
_snputc.o,0.031221%,16,0,16,0,0,0
sys_wrch.o,0.027318%,14,0,14,0,0,0
h1_init.o,0.027318%,14,0,14,0,0,0
defsig_rtmem_outer.o,0.027318%,14,0,14,0,0,0
__printf_wp.o,0.027318%,14,0,14,0,0,0
dretinf.o,0.023416%,12,0,12,0,0,0
sys_exit.o,0.023416%,12,0,12,0,0,0
__rtentry2.o,0.023416%,12,0,12,0,0,0
fpinit.o,0.019513%,10,0,10,0,0,0
rtexit2.o,0.019513%,10,0,10,0,0,0
defsig_exit.o,0.019513%,10,0,10,0,0,0
_sputc.o,0.019513%,10,0,10,0,0,0
_printf_ll.o,0.019513%,10,0,10,0,0,0
_printf_l.o,0.019513%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015610%,8,0,8,0,0,0
libspace.o,0.015610%,8,96,8,0,0,96
__main.o,0.015610%,8,0,8,0,0,0
heapauxi.o,0.011708%,6,0,6,0,0,0
_printf_x.o,0.011708%,6,0,6,0,0,0
_printf_u.o,0.011708%,6,0,6,0,0,0
_printf_s.o,0.011708%,6,0,6,0,0,0
_printf_p.o,0.011708%,6,0,6,0,0,0
_printf_o.o,0.011708%,6,0,6,0,0,0
_printf_n.o,0.011708%,6,0,6,0,0,0
_printf_ls.o,0.011708%,6,0,6,0,0,0
_printf_llx.o,0.011708%,6,0,6,0,0,0
_printf_llu.o,0.011708%,6,0,6,0,0,0
_printf_llo.o,0.011708%,6,0,6,0,0,0
_printf_lli.o,0.011708%,6,0,6,0,0,0
_printf_lld.o,0.011708%,6,0,6,0,0,0
_printf_lc.o,0.011708%,6,0,6,0,0,0
_printf_i.o,0.011708%,6,0,6,0,0,0
_printf_g.o,0.011708%,6,0,6,0,0,0
_printf_f.o,0.011708%,6,0,6,0,0,0
_printf_e.o,0.011708%,6,0,6,0,0,0
_printf_d.o,0.011708%,6,0,6,0,0,0
_printf_c.o,0.011708%,6,0,6,0,0,0
_printf_a.o,0.011708%,6,0,6,0,0,0
__rtentry4.o,0.011708%,6,0,6,0,0,0
printf2.o,0.007805%,4,0,4,0,0,0
printf1.o,0.007805%,4,0,4,0,0,0
hguard.o,0.007805%,4,0,4,0,0,0
_printf_percent_end.o,0.007805%,4,0,4,0,0,0
use_no_semi.o,0.003903%,2,0,2,0,0,0
rtexit.o,0.003903%,2,0,2,0,0,0
libshutdown2.o,0.003903%,2,0,2,0,0,0
libshutdown.o,0.003903%,2,0,2,0,0,0
libinit.o,0.003903%,2,0,2,0,0,0
