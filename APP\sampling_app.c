/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 周期采样应用层实现
*/

#include "sampling_app.h"
#include "config_app.h"

// 全局变量定义
sampling_state_t sampling_state = SAMPLING_STOP;
sampling_cycle_t current_sampling_cycle = SAMPLING_CYCLE_5S; // 默认5秒
uint32_t led_blink_timer = 0;
uint32_t sampling_timer = 0;
uint8_t led_blink_state = 0;
uint8_t sampling_count = 0;
file_storage_t file_storage = {0}; // 文件存储状态

extern uint16_t adc_value[1];
extern config_params_t config_params; // 引用配置参数
extern uint8_t ucLed[6]; // LED状态数组

void sampling_app_init(void)
{
    sampling_state = SAMPLING_STOP;
    current_sampling_cycle = SAMPLING_CYCLE_5S; // 默认5秒采样
    led_blink_timer = 0;
    sampling_timer = 0;
    led_blink_state = 0;
    sampling_count = 0;
    LED1_OFF; // 确保LED1初始状态为关闭
    LED2_OFF; // 确保LED2初始状态为关闭

    // 初始化文件存储系统
    sampling_storage_init();
}

float adc_to_voltage(uint16_t adc_val)
{
    // ADC参考电压3.3V，12位ADC (0-4095)
    return (float)adc_val * 3.3f / 4095.0f;
}

float get_display_voltage(uint16_t adc_val)
{
    // 获取真实电压
    float real_voltage = adc_to_voltage(adc_val);
    // 乘以ratio系数
    float display_voltage = real_voltage * config_params.ratio * 1.0f;

    // 调试信息（可选）
    // my_printf(DEBUG_USART, "DEBUG: ADC=%d, Real=%.3fV, Ratio=%.2f, Display=%.3fV\r\n",
    //          adc_val, real_voltage, config_params.ratio, display_voltage);

    return display_voltage;
}

uint8_t check_voltage_limit(float voltage)
{
    // 检查电压是否超过limit阈值
    return (voltage > config_params.limit) ? 1 : 0;
}

void sampling_set_cycle(sampling_cycle_t cycle)
{
    current_sampling_cycle = cycle;
    my_printf(DEBUG_USART, "Sampling cycle changed to: %dms\r\n", current_sampling_cycle);

    // 如果正在采样，重置采样定时器
    if(sampling_state == SAMPLING_START) {
        sampling_timer = get_system_ms();
        my_printf(DEBUG_USART, "Sampling timer reset\r\n");
    }
}

void sampling_start(void)
{
    if(sampling_state == SAMPLING_STOP) {
        sampling_state = SAMPLING_START;
        led_blink_timer = get_system_ms();
        sampling_timer = get_system_ms();
        sampling_count = 0;

        my_printf(DEBUG_USART, "Period Sampling\r\n");
        my_printf(DEBUG_USART, "sample cycle :%ds\r\n", current_sampling_cycle / 1000);

        // 调试信息：显示当前配置参数
//        my_printf(DEBUG_USART, "Current config - Ratio: %.2f, Limit: %.2f\r\n",
//                 config_params.ratio, config_params.limit);

        // 立即输出第一次采样
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        float real_voltage = adc_to_voltage(adc_value[0]);
        float display_voltage = get_display_voltage(adc_value[0]);

        // 调试信息：显示电压计算过程
//        my_printf(DEBUG_USART, "ADC: %d, Real: %.3fV, Display: %.3fV\r\n",
//                 adc_value[0], real_voltage, display_voltage);

        // 检查是否超限
        if(check_voltage_limit(display_voltage)) {
            my_printf(DEBUG_USART, "%s ch0 = %.2fV, OverLimit(%.2f)!\r\n",
                     time_str, display_voltage, config_params.limit);
            // 保存超限数据到文件
            sampling_save_overlimit_data(time_str, display_voltage, config_params.limit);
            // 点亮LED2
            ucLed[1] = 1;
        } else {
            my_printf(DEBUG_USART, "%s ch0 = %.2fV\r\n", time_str, display_voltage);
            // 保存正常数据到文件
            sampling_save_normal_data(time_str, display_voltage);
            // 关闭LED2
            ucLed[1] = 0;
        }

        sampling_count = 1;
    }
}

void sampling_stop(void)
{
    if(sampling_state == SAMPLING_START) {
        sampling_state = SAMPLING_STOP;
        LED1_OFF; // 关闭LED1
        led_blink_state = 0;
        ucLed[1] = 0; // 关闭LED2

        my_printf(DEBUG_USART, "Periodic Sampling  STOP\r\n");
    }
}

void sampling_toggle(void)
{
    if(sampling_state == SAMPLING_START) {
        sampling_stop();
    } else {
        sampling_start();
    }
}

void sampling_task(void)
{
    uint32_t current_time = get_system_ms();

    if(sampling_state == SAMPLING_START) {
        // LED1闪烁控制 (1秒周期)
        if(current_time - led_blink_timer >= 500) { // 500ms切换一次，形成1秒周期
            led_blink_timer = current_time;
            led_blink_state = !led_blink_state;
            LED1_SET(led_blink_state);
        }

        // 可变周期采样
        if(current_time - sampling_timer >= current_sampling_cycle) {
            sampling_timer = current_time;

            char time_str[32];
            rtc_get_time_string(time_str, sizeof(time_str));
            float real_voltage = adc_to_voltage(adc_value[0]);
            float display_voltage = get_display_voltage(adc_value[0]);

            // 调试信息：显示电压计算过程（可选）
            // my_printf(DEBUG_USART, "ADC: %d, Real: %.3fV, Display: %.3fV\r\n",
            //          adc_value[0], real_voltage, display_voltage);

            // 检查是否超限
            if(check_voltage_limit(display_voltage)) {
                my_printf(DEBUG_USART, "%s ch0 = %.2fV, OverLimit(%.2f)!\r\n",
                         time_str, display_voltage, config_params.limit);
                // 保存超限数据到文件
                sampling_save_overlimit_data(time_str, display_voltage, config_params.limit);
                // 点亮LED2
                ucLed[1] = 1;
            } else {
                my_printf(DEBUG_USART, "%s ch0 = %.2fV\r\n", time_str, display_voltage);
                // 保存正常数据到文件
                sampling_save_normal_data(time_str, display_voltage);
                // 关闭LED2
                ucLed[1] = 0;
            }

            sampling_count++;
        }
    }
}

// 文件存储相关函数实现

/**
 * @brief 初始化存储系统
 */
void sampling_storage_init(void)
{
    // 初始化文件存储状态
    file_storage.sample_record_count = 0;
    file_storage.overlimit_record_count = 0;
    memset(file_storage.current_sample_filename, 0, sizeof(file_storage.current_sample_filename));
    memset(file_storage.current_overlimit_filename, 0, sizeof(file_storage.current_overlimit_filename));

    // 创建存储文件夹
    sampling_create_folders();
}

/**
 * @brief 创建存储文件夹
 */
void sampling_create_folders(void)
{
    FRESULT result;

    // 创建sample文件夹
    result = f_mkdir(SAMPLE_FOLDER_PATH);
    if(result == FR_OK) {
        my_printf(DEBUG_USART, "Sample folder created successfully\r\n");
    } else if(result == FR_EXIST) {
        my_printf(DEBUG_USART, "Sample folder already exists\r\n");
    } else {
        my_printf(DEBUG_USART, "Sample folder creation failed: %d\r\n", result);
    }

    // 创建overLimit文件夹
    result = f_mkdir(OVERLIMIT_FOLDER_PATH);
    if(result == FR_OK) {
        my_printf(DEBUG_USART, "OverLimit folder created successfully\r\n");
    } else if(result == FR_EXIST) {
        my_printf(DEBUG_USART, "OverLimit folder already exists\r\n");
    } else {
        my_printf(DEBUG_USART, "OverLimit folder creation failed: %d\r\n", result);
    }
}

/**
 * @brief 生成文件名，格式：prefix{datetime}.txt
 * @param buffer 输出缓冲区
 * @param prefix 文件名前缀
 * @param buffer_size 缓冲区大小
 */
void sampling_generate_filename(char* buffer, const char* prefix, uint8_t buffer_size)
{
    extern rtc_time_t current_time;

    // 生成文件名：prefix20250115143010.txt
    snprintf(buffer, buffer_size, "%s20%02d%02d%02d%02d%02d%02d.txt",
             prefix,
             current_time.year, current_time.month, current_time.day,
             current_time.hour, current_time.minute, current_time.second);
}

/**
 * @brief 保存数据到文件
 * @param folder_path 文件夹路径
 * @param filename 文件名（会被更新）
 * @param record_count 记录计数（会被更新）
 * @param data 要保存的数据
 * @return 1:成功 0:失败
 */
uint8_t sampling_save_to_file(const char* folder_path, char* filename, uint8_t* record_count, const char* data)
{
    FIL file;
    FRESULT result;
    UINT bytes_written;
    char full_path[128];

    // 如果记录数达到最大值或文件名为空，创建新文件
    if(*record_count >= MAX_RECORDS_PER_FILE || strlen(filename) == 0) {
        // 生成新文件名
        if(strstr(folder_path, "sample") != NULL) {
            sampling_generate_filename(filename, "sampleData", 64);
        } else {
            sampling_generate_filename(filename, "overLimit", 64);
        }
        *record_count = 0;

        // 调试信息：显示新文件创建（可选）
        // my_printf(DEBUG_USART, "Creating new file: %s\r\n", filename);
    }

    // 构建完整路径
    snprintf(full_path, sizeof(full_path), "%s/%s", folder_path, filename);

    // 调试信息：显示文件路径（可选）
    // my_printf(DEBUG_USART, "Writing to: %s\r\n", full_path);

    // 打开文件（追加模式）
    result = f_open(&file, full_path, FA_OPEN_ALWAYS | FA_WRITE);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "File open failed: %d\r\n", result);
        return 0;
    }

    // 移动到文件末尾
    result = f_lseek(&file, f_size(&file));
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "File seek failed: %d\r\n", result);
        f_close(&file);
        return 0;
    }

    // 写入数据
    result = f_write(&file, data, strlen(data), &bytes_written);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "File write failed: %d\r\n", result);
        f_close(&file);
        return 0;
    }

    // 强制同步到TF卡
    result = f_sync(&file);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "File sync failed: %d\r\n", result);
    }

    // 关闭文件
    f_close(&file);

    if(bytes_written == strlen(data)) {
        (*record_count)++;
        // my_printf(DEBUG_USART, "Data saved successfully, record count: %d\r\n", *record_count);
        return 1;
    } else {
        my_printf(DEBUG_USART, "Write incomplete: %d/%d bytes\r\n", bytes_written, strlen(data));
        return 0;
    }
}

/**
 * @brief 保存正常采样数据
 * @param time_str 时间字符串
 * @param voltage 电压值
 */
void sampling_save_normal_data(const char* time_str, float voltage)
{
    char data_line[128];

    // 格式化数据行：时间戳 电压值\r\n
    snprintf(data_line, sizeof(data_line), "%s %.2fV\r\n", time_str, voltage);

    // 保存到正常采样文件
    sampling_save_to_file(SAMPLE_FOLDER_PATH,
                         file_storage.current_sample_filename,
                         &file_storage.sample_record_count,
                         data_line);
}

/**
 * @brief 保存超限数据
 * @param time_str 时间字符串
 * @param voltage 电压值
 * @param limit 限制值
 */
void sampling_save_overlimit_data(const char* time_str, float voltage, float limit)
{
    char data_line[128];

    // 格式化数据行：时间戳 电压值 limit 限制值\r\n
    snprintf(data_line, sizeof(data_line), "%s %.2fV limit %.2fV\r\n", time_str, voltage, limit);

    // 保存到超限文件
    sampling_save_to_file(OVERLIMIT_FOLDER_PATH,
                         file_storage.current_overlimit_filename,
                         &file_storage.overlimit_record_count,
                         data_line);
}
