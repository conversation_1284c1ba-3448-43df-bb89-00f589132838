# GD32F470 ??????????????

## ??????

???????????????????????????GD32F470VET6???????????????????GD32????????????????????????????????????????????????????????????????GD32?????????????????

## ?????
- ???????: GD32F470VET6 (ARM Cortex-M4, ??????200MHz)
- ??????: CMIC_GD32F470VET6 (???????????????????)
- ????????:
  - LED (6?????LED)
  - ???? (6?????????+1????????)
  - OLED????? (0.91???, SSD1306, I2C???)
  - SPI Flash (GD25QXX???)
  - SDIO??? (???SD??)
  - USART??????
  - ADC????????
  - DAC????????

## ???????

??????????????????????????:

1. **?????? (Driver)**
   - GD32F4XX????????
   - CMSIS?????

2. **????? (Components)**
   - `bsp`: ???????????????????????????
   - `ebtn`: ????????????????????????????
   - `gd25qxx`: SPI Flash?????????????????DMA??????
   - `oled`: OLED?????????????????????????????
   - `sdio`: SD?????????

3. **????? (APP)**
   - `btn_app`: ??????????
   - `led_app`: LED???????
   - `oled_app`: OLED??????
   - `usart_app`: ??????????
   - `scheduler`: ?????????????

## ????????

1. **???????????**
   - GPIO???????????????
   - ????DMA?????SPI???
   - I2C?????????????????
   - SDIO??????
   - 12??DAC??ADC???

2. **????????????**
   - ??????????????
   - ???????????????
   - ????????????????

3. **?????????**
   - LED??????????
   - ????????????
   - OLED?????
   - ??????????
   - ADC??????????????

## ??????

### ???????????
- MDK-ARM 5.25??????
- IAR EWARM 8.20??????
- GCC ARM?????? (???)

### ????????? (MDK-ARM)
1. ??`MDK/Project.uvprojx`???????
2. ??????: Project -> Rebuild all target files
3. ???????: Debug -> Start/Stop Debug Session
4. ????????: Debug -> Run (F5)

### ????????? (IAR EWARM)
1. ??EWARM???????
2. ???????????: Project -> Rebuild all
3. ?????????: Project -> Debug
4. ????????: Debug -> Go (F5)

## ????????

???????????????????????????????????????????????????????:

1. **???????????????**
   - ??`Components`???????????????????
   - ???????????????????

2. **??????????**
   - ??`APP`???????????????
   - ??`scheduler.c`????????????

3. **?????????**
   - ???????????`USER/src/main.c`
   - ????????????????????????????????????

## ???????

1. ??????????????`Docs`??????????????????????????????????
2. ???DMA??????????????????????????????DMA????????????????
3. ????????????????????????????????????????DMA??
4. OLED??SPI Flash????????????????????????????????????????????????

## ??????????

### SPI Flash DMA????
???????????SPI Flash??DMA????????????????????????????????????????????????????

### OLED I2C??????????
???I2C?????????????????????????????????????????????OLED???????????

### DAC???????
???DAC??????????????????????????????????????????

### ADC??????????
????????????????ADC???????????
- **????1 (USER_BUTTON_0)**: ???ADC?????????100ms??????ADC??????????SD?????
- **????2 (USER_BUTTON_1)**: ??ADC?????????????????????????????????
- **??????**: `ADC????-????\r\n` (????: `2048-12345\r\n`)
- **???????**: ????????1????????? (????: `ADC_12345.txt`)
- **???????**: SD?????? (`0:/ADC_xxxxx.txt`)

## ?????????

????????McuSTUDIO??????????????????????????????????????????????????GD32????????????????(GigaDevice)??????

## ??????

?????????????????????????

---

*????????2025??6??5??*

## ??????????

### ADC?????????????????????
???"??????????????"????????????????????

1. **?????????????**??
   - ?????????????????????????
   - ?????????????????????????????

2. **?????????**??
   - ??????????????????
   - ???????????????????
   - ???????????????

3. **?????????**??
   - ??????????????????????
   - ?????????????????
   - ????????????????

4. **???????**??
   - ??????????????????
   - ??????????????????
   - ?????????????????
### ?????????? (Error 6 ???)

#### ???????????
- **????1 (USER_BUTTON_0)**: ???ADC??????
- **????2 (USER_BUTTON_1)**: ??ADC?????????????????
- **????3 (USER_BUTTON_2)**: ????????????????????

#### ??????????? (???Error 6: FR_DENIED)
1. **???????????**: ?????????????????????
2. **???????????**: ???????????????????????????
3. **?????????**: ?????????????????????
4. **?????????????**: ?????????????��????? `ADC.txt`
5. **??????????**: ??????��?????????????????????

#### ??????????
```
File system status: OK
Free clusters: 15234
Cluster size: 1024 bytes
Free space: 15234 KB
ADC recording started: 0:/ADC_12345.txt
```

## FATFS���ļ���֧�� (_USE_LFN=3)

### ����API�ӿ�ʵ��

������`_USE_LFN=3`�����ڶѵĳ��ļ���֧�֣�ʱ����Ҫʵ������API�ӿڣ�

#### 1. Unicode��������
- **`ff_convert()`**: Unicode�ַ�ת������
  - ֧��Unicode��OEM����ҳ��˫��ת��
  - ��ʵ�֣�֧�ֻ���ASCII�ͳ��������ַ�
  - ��չ�ַ��Զ�ת��ΪASCII�����ַ�

- **`ff_wtoupper()`**: Unicode�ַ�ת��д����
  - ֧��ASCII�ַ�a-zתA-Z
  - ֧�ֳ��������ַ��Ĵ�Сдת��
  - δ֪�ַ�����ԭ��

#### 2. �ڴ��������
- **`ff_memalloc()`**: ��̬�ڴ����
  - ���ڱ�׼��malloc()ʵ��
  - ����LFN��������������

- **`ff_memfree()`**: ��̬�ڴ��ͷ�
  - ���ڱ�׼��free()ʵ��
  - �ͷ�LFN����������

#### 3. ʱ�������
- **`get_fattime()`**: ��ȡFAT�ļ�ϵͳʱ���
  - ����ϵͳ�������������ʱ���
  - ����FAT32��׼ʱ���ʽ
  - ֧���ļ��������޸�ʱ���¼

### ʵ���ļ�
- `Components/fatfs/fatfs_unicode.c` - Unicode��������ʵ��
- `Components/fatfs/fatfs_unicode.h` - ��������ͷ�ļ�
- `Components/fatfs/diskio.c` - ʱ�������ʵ��

### ����˵��
```c
#define _USE_LFN    3       // ���û��ڶѵĳ��ļ���֧��
#define _MAX_LFN    255     // ����ļ�������
#define _CODE_PAGE  932     // ����Shift-JIS����ҳ
```

## 系统逻辑层功能实现

### 系统启动功能
- **系统初始化打印**: 系统上电后串口自动打印 `====system init====`
- **设备ID读取**: 从Flash地址0x1000读取设备ID，如未初始化则写入默认ID `2025-CIMC-2025680547`
- **设备ID显示**: 串口打印格式 `Device_ID:2025-CIMC-2025680547`

### RTC时钟配置功能
通过串口命令实现RTC时钟的配置和查询：

#### 1. RTC配置命令
- **输入**: `RTC Config`
- **输出**: `Input Datetime`
- **说明**: 进入时间配置模式

#### 2. 时间设置命令
- **输入格式**: `2025-01-01 12:00:30` (年-月-日 时:分:秒)
- **输出**:
  ```
  RTC Config success
  Time:2025-01-01 12:00:30
  ```
- **说明**: 设置系统时钟为指定时间

#### 3. 当前时间查询
- **输入**: `RTC now`
- **输出**: `Current TIME:2025-01-01 12:00:35` (显示当前实时时间)
- **说明**: 查询当前系统时间

### OLED显示功能
- **第一行**: 显示 `system idle` (系统空闲状态)
- **第二行**: 显示当前时间 (格式: 2025-01-01 12:00:30)
- **第三行**: 显示串口状态和按键状态
- **第四行**: 显示ADC采样值

### 串口命令处理
- **波特率**: 115200
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **命令格式**: 以回车换行结束
- **回显功能**: 未识别的命令会进行回显

### Flash存储管理
- **设备ID存储地址**: 0x1000
- **存储格式**: 字符串格式，以NULL结尾
- **自动初始化**: 首次使用时自动写入默认设备ID
- **读写保护**: 使用扇区擦除确保数据完整性

### 使用示例
```
====system init====
Device_ID:2025-CIMC-C84017FF

> RTC Config
Input Datetime
> 2025-01-01 12:00:30
RTC Config success
Time:2025-01-01 12:00:30
> RTC now
Current TIME:2025-01-01 12:00:35
```

### 功能修复说明
1. **串口数据接收优化**: 修复了DMA接收数据长度计算问题，确保命令正确解析
2. **时间同步修复**: 设置新时间时自动重置时间更新计数器，确保时间从设置时刻开始准确计时
3. **设备ID显示**: 设备ID现在显示实际的Flash ID号（如C84017FF），格式为2025-CIMC-xxxxxxxx
4. **命令格式验证**: 增强了时间格式验证，确保输入的时间格式正确
5. **调试信息**: 添加了命令接收调试信息，便于排查问题

## 系统自检功能

### 自检命令
- **输入**: `test`
- **功能**: 执行系统全面自检，包括Flash、TF卡、RTC等模块

### 自检输出格式
```
======system selftest ======
flash..........ok
TF card........ok
flash ID:GD25Q64C
TF card memory:7.45GB
RTC :2025-01-01 12:00:30
======system selftest ======
```

### 失败情况输出格式
```
======system selftest ======
flash..........fail
TF card........fail
can not find TF card
RTC :2025-01-01 12:00:30
======system selftest ======
```

### 自检项目说明

#### 1. Flash自检
- **检测项目**: SPI Flash通信状态和芯片识别
- **成功输出**: `flash..........ok` + `flash ID:芯片型号`
- **失败输出**: `flash..........fail`
- **支持芯片**: GD25Q系列、W25Q系列、EN25QH系列、XM25QH系列等

#### 2. TF卡自检
- **检测项目**: TF卡插入状态、初始化状态、容量读取
- **成功输出**: `TF card........ok` + `TF card memory:容量信息`
- **失败输出**: `TF card........fail` + `can not find TF card`
- **容量显示**: 自动选择合适单位（MB/GB）

#### 3. RTC自检
- **检测项目**: 实时时钟当前时间
- **输出格式**: `RTC :YYYY-MM-DD HH:MM:SS`
- **说明**: 显示当前系统时间

### 注意事项
- 确保串口工具发送命令时包含回车换行符（\r\n）
- 时间格式必须严格按照 "YYYY-MM-DD HH:MM:SS" 格式输入
- 系统会自动过滤命令前后的空格和换行符
- Flash ID会根据实际硬件显示不同的值
- TF卡自检会尝试3次初始化，确保检测准确性

## 周期采样功能

### 采样控制命令

#### 1. 开始采样
- **输入**: `start`
- **功能**: 启动周期采样模式
- **LED状态**: LED1开始1秒周期闪烁
- **输出格式**:
  ```
  Period Sampling
  sample cycle :5s
  2025-01-01 12:00:30 ch0 = 1.65V
  2025-01-01 12:00:35 ch0 = 1.67V
  ```

#### 2. 停止采样
- **输入**: `stop`
- **功能**: 停止周期采样模式
- **LED状态**: LED1停止闪烁并关闭
- **输出格式**: `Periodic Sampling  STOP`

#### 3. 按键控制
- **按键1**: 切换采样状态（start ↔ stop）
- **功能**: 与串口命令效果相同，但无串口输出

### OLED显示模式

#### 采样状态 (start)
- **第一行**: 时间显示 (格式: HH:MM:SS)
- **第二行**: 电压值 (格式: X.XXV，保留两位小数)
- **更新频率**: 实时更新（非采样周期）

#### 停止状态 (stop)
- **第一行**: "system idle"
- **第二行**: 空白

### 按键控制功能
- **按键1**: 切换采样状态（start ↔ stop）
- **按键2**: 设置5秒采样周期
- **按键3**: 设置10秒采样周期
- **按键4**: 设置15秒采样周期

### 电压显示计算
- **显示电压** = 真实电压 × ratio / 100
- **真实电压** = ADC值 × 3.3V / 4095
- **超限检测**: 当显示电压 > limit时触发报警

### 技术参数
- **采样周期**: 5秒/10秒/15秒（可通过按键切换）
- **LED闪烁周期**: 1秒 (500ms亮，500ms灭)
- **ADC分辨率**: 12位 (0-4095)
- **电压范围**: 0-3.3V（真实电压）
- **显示电压**: 真实电压 × ratio系数
- **电压精度**: 保留两位小数
- **OLED更新**: 与串口数据同步，非实时

### 使用示例

#### 正常采样
```
> start
Period Sampling
sample cycle :5s
2025-01-01 12:00:30 ch0 = 8.25V
2025-01-01 12:00:35 ch0 = 8.35V
2025-01-01 12:00:40 ch0 = 8.30V
> stop
Periodic Sampling  STOP
```

#### 超限报警
```
> start
Period Sampling
sample cycle :10s
2025-01-01 12:00:30 ch0 = 15.50V, OverLimit(10.00)!
2025-01-01 12:00:40 ch0 = 16.20V, OverLimit(10.00)!
```

#### 按键操作
- 按下按键2：切换到5秒采样（会显示"Set sampling cycle: 5s"）
- 按下按键3：切换到10秒采样（会显示"Set sampling cycle: 10s"）
- 按下按键4：切换到15秒采样（会显示"Set sampling cycle: 15s"）

#### 电压测试命令
- **输入**: `voltage test`
- **输出**: 显示当前ADC值、真实电压、ratio系数、显示电压、limit阈值和是否超限

### 注意事项
- 采样开始后立即输出第一次采样结果
- 后续每5秒输出一次采样结果
- OLED显示与串口输出同步，不是实时更新
- 按键1可以在任何时候切换采样状态
- LED1闪烁仅在采样状态下有效

## 配置文件管理功能

### 配置文件位置
- **文件路径**: `0:/TF_demo/config.ini`
- **文件格式**: INI格式，包含Ratio和Limit参数

### 配置文件内容示例
```ini
Ratio=75.50
Limit=300.00
```

### Flash存储功能
- **存储地址**: Flash地址0x2000
- **数据格式**: 二进制结构体，包含魔数验证
- **掉电保存**: 参数存储在Flash中，掉电后不丢失

### 配置管理命令

#### 1. 读取TF卡配置文件
- **输入**: `conf`
- **成功输出**:
  ```
  Ratio = 75.50
  Limit = 300.00
  config read success
  ```
- **失败输出**: `config ini not found`

#### 2. 保存参数到Flash
- **输入**: `config save`
- **输出**:
  ```
  Ratio = 75.50
  Limit = 300.00
  save parameters to flash
  ```
- **功能**: 读取当前参数并保存到Flash，掉电不丢失

#### 3. 从Flash读取参数
- **输入**: `config read`
- **输出**:
  ```
  read parameters from flash
  Ratio = 75.50
  Limit = 300.00
  ```
- **功能**: 从Flash读取保存的参数

#### 4. 修改Ratio参数
- **输入**: `ratio`
- **输出**:
  ```
  Ratio = 75.50
  Input value(0-100)
  ```
- **输入新值**: 例如 `85.5`
- **成功输出**:
  ```
  ratio modified success
  Ratio = 85.50
  ```
- **失败输出**:
  ```
  ratio invalid
  Ratio = 75.50
  ```

#### 5. 修改Limit参数
- **输入**: `limit`
- **输出**:
  ```
  Limit = 300
  Input value(0~500)
  ```
- **输入新值**: 例如 `450`
- **成功输出**:
  ```
  limit modified success
  Limit = 450
  ```
- **失败输出**:
  ```
  limit invalid
  Limit = 300
  ```

### 参数范围
- **Ratio**: 0.00 - 100.00 (浮点数，保留两位小数，电压系数)
- **Limit**: 0.00 - 200.00 (浮点数，保留两位小数，超限阈值)

### 文件操作特性
- **自动创建**: 如果TF_demo目录不存在，系统会自动创建
- **实时保存**: 参数修改后立即保存到TF卡
- **错误处理**: 无效输入不会修改原有参数
- **格式验证**: 严格验证参数范围

### 使用示例
```
> conf
Ratio = 50.00
Limit = 100.00
config read success

> config save
Ratio = 50.00
Limit = 100.00
save parameters to flash

> config read
read parameters from flash
Ratio = 50.00
Limit = 100.00

> ratio
Ratio = 50.00
Input value(0-100)
> 75.5
ratio modified success
Ratio = 75.50

> limit
Limit = 100.00
Input value(0~200)
> 150.5
limit modified success
Limit = 150.50

> ratio
Ratio = 75.50
Input value(0-100)
> 150
ratio invalid
Ratio = 75.50
```

### 注意事项
- 确保TF卡已正确插入并初始化
- 配置文件使用标准INI格式
- 参数修改会立即写入TF卡
- Flash存储地址为0x2000，请确保不与其他数据冲突
- 系统启动时优先从Flash读取参数，如无有效数据则使用默认值
- 默认值：Ratio=50.00, Limit=100.00
- Limit参数现在支持浮点数，保留两位小数
- 输入超出范围的值不会被保存
- Flash数据包含魔数验证，确保数据完整性

## TF卡数据存储功能

### 存储文件夹结构
系统会在TF卡根目录自动创建以下文件夹：
- **sample文件夹**: 存储正常采样数据
- **overLimit文件夹**: 存储超限采样数据

### 文件命名规则
- **正常采样文件**: `SAM001.TXT`, `SAM002.TXT`, `SAM003.TXT` ...
- **超限采样文件**: `LIM001.TXT`, `LIM002.TXT`, `LIM003.TXT` ...
- **命名格式**: 使用8.3格式确保最大兼容性，文件按创建顺序编号

### 文件存储规则
- **每文件记录数**: 最多10条数据记录
- **自动分文件**: 超过10条记录后自动创建新文件
- **文件创建时间**: 以文件创建时的系统时间命名

### 数据格式

#### 正常采样数据格式
```
2025-01-01 14:30:10 8.25V
2025-01-01 14:30:15 8.35V
2025-01-01 14:30:20 8.30V
```

#### 超限采样数据格式
```
2025-01-01 14:30:10 15.50V limit 10.00V
2025-01-01 14:30:15 16.20V limit 10.00V
2025-01-01 14:30:20 15.80V limit 10.00V
```

### LED指示功能
- **LED1**: 采样状态指示（1秒周期闪烁）
- **LED2**: 超限状态指示（超限时点亮，正常时熄灭）

### 文件示例

#### sample文件夹中的文件
- `SAM001.TXT` (第一个文件，10条记录)
- `SAM002.TXT` (第二个文件，继续记录)
- `SAM003.TXT` (第三个文件，继续记录)

#### overLimit文件夹中的文件
- `LIM001.TXT` (第一个超限数据文件)
- `LIM002.TXT` (第二个超限数据文件)

### 存储特性
- **实时存储**: 每次采样后立即写入TF卡
- **分类存储**: 正常数据和超限数据分别存储
- **读卡器兼容**: 文件可通过读卡器在电脑上查看
- **自动管理**: 系统自动管理文件创建和数据写入
- **容错处理**: TF卡写入失败不影响串口输出和OLED显示

### 使用示例

#### 正常采样存储
```
> start
Period Sampling
sample cycle :5s
2025-01-01 14:30:10 ch0 = 8.25V
2025-01-01 14:30:15 ch0 = 8.35V
```
对应在 `0:/sample/SAM001.TXT` 中存储：
```
2025-01-01 14:30:10 8.25V
2025-01-01 14:30:15 8.35V
```

#### 超限数据存储
```
> start
Period Sampling
sample cycle :5s
2025-01-01 14:30:10 ch0 = 15.50V, OverLimit(10.00)!
2025-01-01 14:30:15 ch0 = 16.20V, OverLimit(10.00)!
```
对应在 `0:/overLimit/LIM001.TXT` 中存储：
```
2025-01-01 14:30:10 15.50V limit 10.00V
2025-01-01 14:30:15 16.20V limit 10.00V
```

### 注意事项
- 确保TF卡已正确插入并格式化为FAT32格式
- 文件夹会在系统初始化时自动创建
- 每个文件最多存储10条记录，超过后自动创建新文件
- 文件名中的时间为文件创建时的系统时间
- LED2状态与当前采样结果实时同步
- 存储功能不影响原有的串口输出和OLED显示功能

### 调试和故障排除

#### 文件存储测试命令

1. **完整功能测试**
- **输入**: `file test`
- **功能**: 测试文件存储功能，创建文件夹并写入测试数据

2. **简单文件测试**
- **输入**: `simple test`
- **功能**: 在根目录创建简单测试文件，验证基本文件操作

3. **RTC时间检查**
- **输入**: `rtc check`
- **功能**: 检查RTC时间值的有效性，排查文件名生成问题

4. **文件名格式测试**
- **输入**: `name test`
- **功能**: 测试不同文件名格式的兼容性，包括8.3格式和长文件名
- **输出示例**:
  ```
  File Storage Test:
  Creating folders...
  Sample folder created successfully
  OverLimit folder already exists
  Testing normal data save...
  Creating new file: sampleData20250115143010.txt
  Writing to: 0:/sample/sampleData20250115143010.txt
  Data saved successfully, record count: 1
  Testing overlimit data save...
  Creating new file: overLimit20250115143010.txt
  Writing to: 0:/overLimit/overLimit20250115143010.txt
  Data saved successfully, record count: 1
  File test completed
  ```

#### 常见问题解决

1. **TF卡上看不到文件**
   - **原因**: 文件系统缓存未同步到TF卡
   - **解决**: 代码已添加 `f_sync()` 强制同步
   - **验证**: 使用 `file test` 命令测试

2. **文件夹创建失败**
   - **原因**: TF卡未正确初始化或格式不兼容
   - **解决**: 确保TF卡格式化为FAT32格式
   - **验证**: 先运行 `test` 命令检查TF卡状态

3. **文件写入失败**
   - **原因**: TF卡空间不足或写保护
   - **解决**: 检查TF卡容量和写保护开关
   - **调试**: 查看串口输出的错误代码

4. **文件名格式错误**
   - **原因**: RTC时间未正确设置
   - **解决**: 使用 `RTC Config` 命令设置正确时间
   - **验证**: 使用 `RTC now` 查看当前时间

#### 调试模式
代码中包含详细的调试信息输出，可以通过以下方式启用：
- 取消注释 `sampling_app.c` 中的调试打印语句
- 观察串口输出的文件操作详细信息
- 使用 `file test` 命令进行功能验证

#### 错误代码说明
- **FR_OK (0)**: 操作成功
- **FR_DISK_ERR (1)**: 磁盘错误
- **FR_INT_ERR (2)**: 内部错误
- **FR_NOT_READY (3)**: 设备未就绪
- **FR_NO_FILE (4)**: 文件不存在
- **FR_NO_PATH (5)**: 路径不存在
- **FR_INVALID_NAME (6)**: 文件名无效
- **FR_DENIED (7)**: 访问被拒绝
- **FR_EXIST (8)**: 文件已存在
