/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include "selftest_app.h"
#include "sampling_app.h"
#include "config_app.h"

__IO uint8_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[256] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[256];
    va_list arg;
    int len;
    // ????????????��?
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
        usart_data_transmit(usart_periph, buffer[tx_count]);
    }
    
    return len;
}

// 解析时间字符串函数
static int parse_datetime(const char* datetime_str, uint8_t* year, uint8_t* month, uint8_t* day, uint8_t* hour, uint8_t* minute, uint8_t* second)
{
    int full_year, temp_month, temp_day, temp_hour, temp_minute, temp_second;

    // 解析格式: "2025-01-01 12:00:30"
    if(sscanf(datetime_str, "%d-%d-%d %d:%d:%d", &full_year, &temp_month, &temp_day, &temp_hour, &temp_minute, &temp_second) == 6) {
        *year = full_year % 100; // 取年份后两位
        *month = temp_month;
        *day = temp_day;
        *hour = temp_hour;
        *minute = temp_minute;
        *second = temp_second;

        // 简单验证
        if(*month >= 1 && *month <= 12 && *day >= 1 && *day <= 31 &&
           *hour <= 23 && *minute <= 59 && *second <= 59) {
            return 1; // 成功
        }
    }
    return 0; // 失败
}

void uart_task(void)
{
    if(!rx_flag) return;

    // 确保字符串以NULL结尾
    uart_dma_buffer[255] = '\0';

    // 移除字符串末尾的换行符和回车符
    char* cmd = (char*)uart_dma_buffer;
    int len = strlen(cmd);

    // 移除末尾的控制字符
    while(len > 0 && (cmd[len-1] == '\r' || cmd[len-1] == '\n' || cmd[len-1] == ' ' || cmd[len-1] == '\t')) {
        cmd[len-1] = '\0';
        len--;
    }

    // 移除开头的空格
    while(*cmd == ' ' || *cmd == '\t') {
        cmd++;
    }

    // 重新计算长度
    len = strlen(cmd);

    // 如果命令为空，直接返回
    if(len == 0 || *cmd == '\0') {
        rx_flag = 0;
        memset(uart_dma_buffer, 0, 256);
        return;
    }

    // 调试信息：显示处理的命令（可选）
    // my_printf(DEBUG_USART, "DEBUG: Processing command [%s] len=%d\r\n", cmd, len);

    // 首先检查是否在等待配置输入状态
    if(config_state != CONFIG_STATE_NORMAL) {
        config_process_input(cmd);
        rx_flag = 0; // 清除接收标志
        memset(uart_dma_buffer, 0, 256);
        return; // 处理完输入后直接返回
    }

    // 命令处理 - 使用更健壮的字符串比较
    if(strncmp(cmd, "test", 4) == 0 && len == 4) {
        system_selftest();
    }
    else if(strncmp(cmd, "start", 5) == 0 && len == 5) {
        sampling_start();
    }
    else if(strncmp(cmd, "stop", 4) == 0 && len == 4) {
        sampling_stop();
    }
    else if(strncmp(cmd, "conf", 4) == 0 && len == 4) {
        config_cmd_handler();
    }
    else if(strncmp(cmd, "config save", 11) == 0 && len == 11) {
        config_save_handler();
    }
    else if(strncmp(cmd, "config read", 11) == 0 && len == 11) {
        config_read_handler();
    }
    else if(strncmp(cmd, "ratio", 5) == 0 && len == 5) {
        config_ratio_handler();
    }
    else if(strncmp(cmd, "limit", 5) == 0 && len == 5) {
        config_limit_handler();
    }
    else if(strncmp(cmd, "RTC Config", 10) == 0 && len == 10) {
        my_printf(DEBUG_USART, "Input Datetime\r\n");
    }
    else if(strncmp(cmd, "RTC now", 7) == 0 && len == 7) {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        my_printf(DEBUG_USART, "Current TIME:%s\r\n", time_str);
    }
    else if(strncmp(cmd, "voltage test", 12) == 0 && len == 12) {
        extern uint16_t adc_value[1];
        float real_voltage = adc_to_voltage(adc_value[0]);
        float display_voltage = get_display_voltage(adc_value[0]);
        my_printf(DEBUG_USART, "Voltage Test:\r\n");
        my_printf(DEBUG_USART, "ADC Value: %d\r\n", adc_value[0]);
        my_printf(DEBUG_USART, "Real Voltage: %.3fV\r\n", real_voltage);
        my_printf(DEBUG_USART, "Ratio: %.2f\r\n", config_params.ratio);
        my_printf(DEBUG_USART, "Display Voltage: %.3fV\r\n", display_voltage);
        my_printf(DEBUG_USART, "Limit: %.2fV\r\n", config_params.limit);
        my_printf(DEBUG_USART, "Over Limit: %s\r\n", check_voltage_limit(display_voltage) ? "YES" : "NO");
    }
    else if(strncmp(cmd, "file test", 9) == 0 && len == 9) {
        // 测试文件存储功能
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));

        my_printf(DEBUG_USART, "File Storage Test:\r\n");
        my_printf(DEBUG_USART, "Current time: %s\r\n", time_str);

        my_printf(DEBUG_USART, "Creating folders...\r\n");
        sampling_create_folders();

        my_printf(DEBUG_USART, "Testing normal data save...\r\n");
        sampling_save_normal_data(time_str, 8.25f);

        my_printf(DEBUG_USART, "Testing overlimit data save...\r\n");
        sampling_save_overlimit_data(time_str, 15.50f, 10.00f);

        my_printf(DEBUG_USART, "File test completed\r\n");
    }
    else if(strncmp(cmd, "simple test", 11) == 0 && len == 11) {
        // 简单文件测试 - 直接在根目录创建文件
        FIL test_file;
        FRESULT result;
        UINT bytes_written;
        char test_data[] = "Hello TF Card!\r\n";

        my_printf(DEBUG_USART, "Simple File Test:\r\n");

        // 尝试在根目录创建文件
        result = f_open(&test_file, "0:/test.txt", FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            my_printf(DEBUG_USART, "File opened successfully\r\n");

            result = f_write(&test_file, test_data, strlen(test_data), &bytes_written);
            if(result == FR_OK) {
                my_printf(DEBUG_USART, "Data written: %d bytes\r\n", bytes_written);
            } else {
                my_printf(DEBUG_USART, "Write failed: %d\r\n", result);
            }

            f_sync(&test_file);
            f_close(&test_file);
            my_printf(DEBUG_USART, "File closed\r\n");
        } else {
            my_printf(DEBUG_USART, "File open failed: %d\r\n", result);
        }
    }
    else if(strncmp(cmd, "rtc check", 9) == 0 && len == 9) {
        // 检查RTC时间
        extern rtc_time_t current_time;
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));

        my_printf(DEBUG_USART, "RTC Check:\r\n");
        my_printf(DEBUG_USART, "Raw time: %02d-%02d-%02d %02d:%02d:%02d\r\n",
                 current_time.year, current_time.month, current_time.day,
                 current_time.hour, current_time.minute, current_time.second);
        my_printf(DEBUG_USART, "Formatted: %s\r\n", time_str);

        // 检查时间值的有效性
        if(current_time.year > 99) my_printf(DEBUG_USART, "Warning: Year > 99\r\n");
        if(current_time.month < 1 || current_time.month > 12) my_printf(DEBUG_USART, "Warning: Invalid month\r\n");
        if(current_time.day < 1 || current_time.day > 31) my_printf(DEBUG_USART, "Warning: Invalid day\r\n");
        if(current_time.hour > 23) my_printf(DEBUG_USART, "Warning: Invalid hour\r\n");
        if(current_time.minute > 59) my_printf(DEBUG_USART, "Warning: Invalid minute\r\n");
        if(current_time.second > 59) my_printf(DEBUG_USART, "Warning: Invalid second\r\n");
    }
    else if(strncmp(cmd, "name test", 9) == 0 && len == 9) {
        // 测试不同的文件名格式
        FIL test_file;
        FRESULT result;
        UINT bytes_written;
        char test_data[] = "Test data\r\n";

        my_printf(DEBUG_USART, "Filename Test:\r\n");

        // 测试1: 8.3格式文件名
        my_printf(DEBUG_USART, "Testing 8.3 format...\r\n");
        result = f_open(&test_file, "0:/TEST1.TXT", FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            f_write(&test_file, test_data, strlen(test_data), &bytes_written);
            f_sync(&test_file);
            f_close(&test_file);
            my_printf(DEBUG_USART, "8.3 format: SUCCESS\r\n");
        } else {
            my_printf(DEBUG_USART, "8.3 format failed: %d\r\n", result);
        }

        // 测试2: 在sample文件夹中创建8.3格式文件
        my_printf(DEBUG_USART, "Testing folder + 8.3 format...\r\n");
        result = f_open(&test_file, "0:/sample/TEST2.TXT", FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            f_write(&test_file, test_data, strlen(test_data), &bytes_written);
            f_sync(&test_file);
            f_close(&test_file);
            my_printf(DEBUG_USART, "Folder + 8.3: SUCCESS\r\n");
        } else {
            my_printf(DEBUG_USART, "Folder + 8.3 failed: %d\r\n", result);
        }

        // 测试3: 长文件名
        my_printf(DEBUG_USART, "Testing long filename...\r\n");
        result = f_open(&test_file, "0:/sample/LongFileName123.txt", FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            f_write(&test_file, test_data, strlen(test_data), &bytes_written);
            f_sync(&test_file);
            f_close(&test_file);
            my_printf(DEBUG_USART, "Long filename: SUCCESS\r\n");
        } else {
            my_printf(DEBUG_USART, "Long filename failed: %d\r\n", result);
        }
    }
    // 处理时间设置命令（格式：2025-01-01 12:00:30）
    else if(len == 19 && cmd[4] == '-' && cmd[7] == '-' && cmd[10] == ' ' && cmd[13] == ':' && cmd[16] == ':') {
        uint8_t year, month, day, hour, minute, second;
        if(parse_datetime(cmd, &year, &month, &day, &hour, &minute, &second)) {
            rtc_set_time(year, month, day, hour, minute, second);
            my_printf(DEBUG_USART, "RTC Config success\r\nTime:%s\r\n", cmd);
        } else {
            my_printf(DEBUG_USART, "Invalid datetime format\r\n");
        }
    }
    // 其他命令回显（用于调试）
    else {
        my_printf(DEBUG_USART, "Unknown command: [%s] (len=%d)\r\n", cmd, len);
        // 显示每个字符的ASCII码用于调试
        my_printf(DEBUG_USART, "ASCII: ");
        for(int i = 0; i < len && i < 20; i++) {
            my_printf(DEBUG_USART, "%02X ", (uint8_t)cmd[i]);
        }
        my_printf(DEBUG_USART, "\r\n");
    }

    rx_flag = 0; // 清除接收标志
    memset(uart_dma_buffer, 0, 256);
}


