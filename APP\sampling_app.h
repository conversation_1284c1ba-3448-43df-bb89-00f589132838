/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 周期采样应用层头文件
*/

#ifndef SAMPLING_APP_H
#define SAMPLING_APP_H

#include "mcu_cmic_gd32f470vet6.h"

// 采样状态枚举
typedef enum {
    SAMPLING_STOP = 0,
    SAMPLING_START = 1
} sampling_state_t;

// 采样周期枚举
typedef enum {
    SAMPLING_CYCLE_5S = 5000,   // 5秒
    SAMPLING_CYCLE_10S = 10000, // 10秒
    SAMPLING_CYCLE_15S = 15000  // 15秒
} sampling_cycle_t;

// 文件存储相关定义
#define MAX_RECORDS_PER_FILE 10 // 每个文件最大记录数
#define SAMPLE_FOLDER_PATH "0:/sample" // 正常采样文件夹路径
#define OVERLIMIT_FOLDER_PATH "0:/overLimit" // 超限文件夹路径

// 文件存储状态结构
typedef struct {
    uint8_t sample_record_count; // 当前正常采样文件记录数
    uint8_t overlimit_record_count; // 当前超限文件记录数
    char current_sample_filename[64]; // 当前正常采样文件名
    char current_overlimit_filename[64]; // 当前超限文件名
} file_storage_t;

// 数据加密状态
typedef enum {
    ENCRYPT_MODE_OFF = 0,  // 关闭加密
    ENCRYPT_MODE_ON = 1    // 开启加密
} encrypt_mode_t;

// 全局变量声明
extern sampling_state_t sampling_state;
extern sampling_cycle_t current_sampling_cycle;
extern uint32_t led_blink_timer;
extern uint32_t sampling_timer;
extern uint8_t led_blink_state;
extern file_storage_t file_storage;
extern encrypt_mode_t encrypt_mode;

// 函数声明
void sampling_app_init(void); // 采样应用初始化
void sampling_start(void); // 开始采样
void sampling_stop(void); // 停止采样
void sampling_toggle(void); // 切换采样状态
void sampling_task(void); // 采样任务
void sampling_set_cycle(sampling_cycle_t cycle); // 设置采样周期
float adc_to_voltage(uint16_t adc_value); // ADC值转电压
float get_display_voltage(uint16_t adc_value); // 获取显示电压（ADC * ratio）
uint8_t check_voltage_limit(float voltage); // 检查电压是否超限

// 文件存储相关函数
void sampling_storage_init(void); // 初始化存储系统
void sampling_create_folders(void); // 创建存储文件夹
void sampling_generate_filename(char* buffer, const char* prefix, uint8_t buffer_size); // 生成文件名
uint8_t sampling_save_to_file(const char* folder_path, char* filename, uint8_t* record_count, const char* data); // 保存数据到文件
void sampling_save_normal_data(const char* time_str, float voltage); // 保存正常采样数据
void sampling_save_overlimit_data(const char* time_str, float voltage, float limit); // 保存超限数据

// 数据加密相关函数
uint32_t rtc_to_unix_timestamp(void); // RTC时间转Unix时间戳
void voltage_to_hex(float voltage, uint16_t* integer_part, uint16_t* decimal_part); // 电压转HEX格式
void sampling_output_encrypted(const char* time_str, float voltage, uint8_t is_overlimit); // 输出加密数据
void sampling_set_encrypt_mode(encrypt_mode_t mode); // 设置加密模式

#endif // SAMPLING_APP_H
