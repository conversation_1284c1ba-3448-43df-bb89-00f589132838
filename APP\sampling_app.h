/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/14
* Note: 周期采样应用层头文件
*/

#ifndef SAMPLING_APP_H
#define SAMPLING_APP_H

#include "mcu_cmic_gd32f470vet6.h"

// 采样状态枚举
typedef enum {
    SAMPLING_STOP = 0,
    SAMPLING_START = 1
} sampling_state_t;

// 采样周期枚举
typedef enum {
    SAMPLING_CYCLE_5S = 5000,   // 5秒
    SAMPLING_CYCLE_10S = 10000, // 10秒
    SAMPLING_CYCLE_15S = 15000  // 15秒
} sampling_cycle_t;

// 全局变量声明
extern sampling_state_t sampling_state;
extern sampling_cycle_t current_sampling_cycle;
extern uint32_t led_blink_timer;
extern uint32_t sampling_timer;
extern uint8_t led_blink_state;

// 函数声明
void sampling_app_init(void); // 采样应用初始化
void sampling_start(void); // 开始采样
void sampling_stop(void); // 停止采样
void sampling_toggle(void); // 切换采样状态
void sampling_task(void); // 采样任务
void sampling_set_cycle(sampling_cycle_t cycle); // 设置采样周期
float adc_to_voltage(uint16_t adc_value); // ADC值转电压
float get_display_voltage(uint16_t adc_value); // 获取显示电压（ADC * ratio）
uint8_t check_voltage_limit(float voltage); // 检查电压是否超限

#endif // SAMPLING_APP_H
